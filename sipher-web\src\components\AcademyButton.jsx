"use client";

import React, { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { GraduationCap, ExternalLink, X } from "lucide-react";

const AcademyButton = () => {
  const [isVisible, setIsVisible] = useState(false);
  const [isHovered, setIsHovered] = useState(false);

  // Show button after a delay when page loads
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsVisible(true);
    }, 2000); // Show after 2 seconds

    return () => clearTimeout(timer);
  }, []);

  const handleAcademyClick = () => {
    // Replace with your actual academy website URL
    window.open("https://sipherweb.com", "_blank", "noopener,noreferrer");
  };

  const handleClose = (e) => {
    e.stopPropagation();
    setIsVisible(false);
  };

  if (!isVisible) return null;

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0, x: -100, scale: 0.8 }}
        animate={{ opacity: 1, x: 0, scale: 1 }}
        exit={{ opacity: 0, x: -100, scale: 0.8 }}
        transition={{ 
          type: "spring", 
          stiffness: 300, 
          damping: 25,
          duration: 0.5 
        }}
        className="fixed bottom-8 left-8 z-40 group"
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
      >
        {/* Main Button */}
        <motion.button
          onClick={handleAcademyClick}
          className="relative bg-gradient-to-r from-[#100562] via-blue-600 to-[#100562] text-white px-6 py-4 rounded-2xl shadow-2xl hover:shadow-3xl transition-all duration-300 flex items-center gap-3 group overflow-hidden"
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
        >
          {/* Animated Background */}
          <motion.div
            className="absolute inset-0 bg-gradient-to-r from-blue-600 via-[#100562] to-blue-600 opacity-0 group-hover:opacity-100 transition-opacity duration-300"
            initial={false}
            animate={isHovered ? { x: ["-100%", "100%"] } : {}}
            transition={{ duration: 1.5, repeat: Infinity, ease: "linear" }}
          />
          
          {/* Icon */}
          <motion.div
            className="relative z-10"
            animate={isHovered ? { rotate: [0, 10, -10, 0] } : {}}
            transition={{ duration: 0.5 }}
          >
            <GraduationCap className="w-6 h-6" />
          </motion.div>
          
          {/* Text */}
          <div className="relative z-10 flex flex-col items-start">
            <span className="text-sm font-bold text-[#FFE300] uppercase tracking-wide">
              Visit Our
            </span>
            <span className="text-lg font-bold leading-tight">
              Academic Website
            </span>
          </div>
          
          {/* External Link Icon */}
          <motion.div
            className="relative z-10"
            animate={isHovered ? { x: 3, y: -3 } : {}}
            transition={{ duration: 0.2 }}
          >
            <ExternalLink className="w-4 h-4 opacity-80" />
          </motion.div>
          
          {/* Close Button */}
          <motion.button
            onClick={handleClose}
            className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200 hover:bg-red-600"
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
          >
            <X className="w-3 h-3" />
          </motion.button>
        </motion.button>

        {/* Pulsing Ring Effect */}
        <motion.div
          className="absolute inset-0 border-2 border-[#FFE300] rounded-2xl opacity-30"
          animate={{
            scale: [1, 1.1, 1],
            opacity: [0.3, 0.1, 0.3],
          }}
          transition={{
            duration: 2,
            repeat: Infinity,
            ease: "easeInOut",
          }}
        />    
        
        {/* Tooltip */}
        <AnimatePresence>
          {isHovered && (
            <motion.div
              initial={{ opacity: 0, y: 10, x: -20 }}
              animate={{ opacity: 1, y: 0, x: 0 }}
              exit={{ opacity: 0, y: 10, x: -20 }}
              className="absolute bottom-full left-0 mb-2 bg-black/80 text-white text-xs px-3 py-2 rounded-lg whitespace-nowrap backdrop-blur-sm"
            >
              Learn new skills at Sipher Academy
              <div className="absolute top-full left-4 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-black/80" />
            </motion.div>
          )}
        </AnimatePresence>
      </motion.div>
    </AnimatePresence>
  );
};

export default AcademyButton;
